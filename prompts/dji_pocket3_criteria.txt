### **第一部分：核心分析原则 (不可违背)**

1. **画像优先原则 (PERSONA-FIRST PRINCIPLE) [V6.3 核心升级]**:
   解决“Vlog玩家”与“专业贩子”识别混淆的最高指导原则。首要任务是**构建一个连贯的卖家“行为画像”**。回答核心问题：“这个卖家的所有行为（买、卖、评价、签名）组合起来，讲述的是一个怎样的故事？”
   - 若故事指向**连贯的个人行为**（例如，一个热爱旅拍，不断体验、升级、出掉旧设备的发烧友），则表面“疑点”可合理解释，**不应**作为否决依据。
   - 若故事**矛盾、不连贯，或明确指向商业行为**（例如，购买记录是配件和维修，售卖记录却是大量“几乎全新”的同型号 Pocket 3），即便伪装得好，也必须判定为商家。
2. **一票否决硬性原则 (HARD DEAL-BREAKER RULES)**:
   任何一项不满足，`is_recommended` 立即为 `false`。
   - **型号/套装**: 必须是 **DJI Pocket 3**（单机或全能套装均可，但需在描述/图片中明确）。
   - **卖家信用**: `卖家信用等级` 必须是 **'卖家信用极好'**。
   - **邮寄方式**: 必须 **支持邮寄**。
   - **电池循环硬性门槛**: 若明确提供了电池循环次数，其数值 **`必须 ≤ 50 次`**（仅适用于全能套装的电池手柄）。
   - **【V6.4 逻辑修正】机器历史**: **不得出现**“摔过”、“维修过”、“更换过镜头模组”、“有暗病”等明确事故或拆修描述。
3. **图片至上原则 (IMAGE-FIRST PRINCIPLE)**: 图片与文本冲突时，以图片为准。
4. **【V6.4 逻辑修正】信息缺失处理原则 (MISSING-INFO HANDLING)**:
   对于可后天询问的关键信息（**电池循环次数**、**维修/摔机历史**），若完全未提及，状态为 `NEEDS_MANUAL_CHECK`，**不直接导致否决**。若卖家画像优秀，可“有条件推荐”。

------

### **第二部分：详细分析指南**

**A. 商品本身评估 (Criteria Analysis):**

1. **型号确认 (`model_package`)**: 文本与图片均需指向 **DJI Pocket 3**。非此型号则 `FAIL`。
2. **电池循环 (`battery_cycles`)**: 全能套装的电池手柄循环次数 ≤ 50 次；若无信息，则为 `NEEDS_MANUAL_CHECK`。
3. **成色外观 (`condition`)**: 最多接受“轻微使用痕迹”。重点检查镜头、屏幕、云台、边框。
4. **【V6.4 逻辑修正】机器历史 (`history`)**: 全面排查“摔过”、“维修”、“进水”、“云台异常”等负面描述。
   - 若完全未提及，状态为 `NEEDS_MANUAL_CHECK`；
   - 若有任何事故或拆修证据，则为 `FAIL`。

**B. 卖家与市场评估 (核心)**

1. **卖家背景深度分析 (`seller_type`) - [决定性评估]**:
   运用“画像优先原则”，判定是【个人玩家】还是【商家/贩子】。
   - **【V6.3 升级】危险信号清单 (Red Flag List) 及豁免条款**:
     - **交易频率**: 短期内密集交易。
       - **【发烧友豁免】**: 时间跨度长（如超2年）且买卖形成“体验-升级-出售”闭环（如玩过 Action 系列、Pocket 2、手机稳定器等），则不适用。
     - **商品垂直度**: 发布商品高度集中于 Pocket 系列或配件。
       - **【发烧友豁免】**: 若购买/评价记录表明其为深度玩家（如讨论画质、配件玩法），专注是专业性的体现，关键看“玩”还是“出货”。
     - **“行话”**: 出现“回收、同行、工作室、置换、量大”等术语。
       - **【无豁免】**: 强烈商家信号。
     - **物料购买**: 批量购入电池手柄、保护壳、维修工具、摔坏的机器。
       - **【无豁免】**: 决定性商家证据。
     - **图片/标题风格**: 背景统一、专业；标题模板化。
       - **【发烧友豁免】**: 若图片传递“爱惜感”（如精心布景、展示个人旅拍作品），是加分项。
2. **邮寄方式 (`shipping`)**: 仅限“同城面交/自提”则 `FAIL`。
3. **卖家信用 (`seller_credit`)**: 必须为 **'卖家信用极好'**。